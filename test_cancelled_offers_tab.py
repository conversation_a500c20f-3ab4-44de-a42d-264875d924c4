#!/usr/bin/env python3
"""
Test script to verify cancelled offers appear in the cancelled tab.

This script tests:
1. Offers with status="cancelled" and active=false appear in cancelled tab
2. Offers are properly categorized by status (active, inactive, cancelled)
3. Offer counts are accurate for each category

Usage:
    python test_cancelled_offers_tab.py
"""

import sys
import os
import logging
from typing import Dict, Any, List
import uuid

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from firebase_admin import firestore

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


class CancelledOffersTabTester:
    """Test suite for cancelled offers tab functionality."""
    
    def __init__(self):
        self.test_brand_id = "test_brand_cancelled_tab"
        self.test_product_id = "test_product_cancelled_tab"
        self.test_offers = []
        self.initial_wallet_balance = 1000.0  # $1000 initial balance
        
    def cleanup(self):
        """Clean up test data."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Delete test offers
            for offer_id in self.test_offers:
                db.collection("offers").document(offer_id).delete()
            
            # Delete test product
            db.collection("products").document(self.test_product_id).delete()
            
            # Delete test wallet and transactions
            wallet_ref = db.collection("wallets").document(self.test_brand_id)
            
            # Delete all transactions
            transactions = wallet_ref.collection("transactions").stream()
            for tx in transactions:
                tx.reference.delete()
            
            # Delete wallet
            wallet_ref.delete()
            
            # Delete test brand
            db.collection("brands").document(self.test_brand_id).delete()
            
        except Exception as e:
            logger.debug(f"Cleanup error (expected): {e}")
        
        logger.info("✅ Cleanup completed")
    
    def setup_test_data(self):
        """Set up test brand, product, and wallet."""
        logger.info("🔧 Setting up test data...")
        
        # Create test brand
        brand_data = {
            "brand_id": self.test_brand_id,
            "company_name": "Test Cancelled Tab Company",
            "website": "https://test-cancelled-tab.com",
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("brands").document(self.test_brand_id).set(brand_data)
        
        # Create test product
        product_data = {
            "id": self.test_product_id,
            "title": "Test Cancelled Tab Product",
            "description": "A test product for cancelled tab testing",
            "brand_id": self.test_brand_id,
            "categories": ["testing"],
            "keywords": ["test", "cancelled", "tab"],
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("products").document(self.test_product_id).set(product_data)
        
        # Create test wallet with initial balance
        wallet_data = {
            "brand_id": self.test_brand_id,
            "total_available_balance": self.initial_wallet_balance,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("wallets").document(self.test_brand_id).set(wallet_data)
        
        logger.info(f"✅ Test data setup complete. Initial wallet balance: ${self.initial_wallet_balance}")
    
    def create_test_offer(self, status: str, active: bool, title_suffix: str) -> str:
        """Create a test offer with specified status."""
        offer_id = str(uuid.uuid4())
        self.test_offers.append(offer_id)
        
        # Create offer
        offer_data = {
            "id": offer_id,
            "brand_id": self.test_brand_id,
            "product_id": self.test_product_id,
            "offer_title": f"Test Offer {title_suffix}",
            "offer_description": f"Test offer for {status} status testing",
            "active": active,
            "status": status,  # Store in lowercase
            "offer_total_budget_allocated": 100.0,
            "offer_total_budget_spent": 0.0,
            "offer_total_promo_spent": 0.0,
            "remaining_budget": 100.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        
        if status == "cancelled":
            offer_data["cancelled_at"] = firestore.SERVER_TIMESTAMP
        
        db.collection("offers").document(offer_id).set(offer_data)
        
        logger.info(f"✅ Created {status} offer: {offer_id}")
        return offer_id
    
    def simulate_offers_api_call(self) -> Dict[str, Any]:
        """Simulate the get_all_brand_offers API call."""
        # Get all offers for the brand
        offers_query = db.collection("offers").where("brand_id", "==", self.test_brand_id)
        offers_snapshot = offers_query.stream()
        
        offers = []
        for doc in offers_snapshot:
            data = doc.to_dict()
            offer_id = doc.id
            
            # Simulate the offer object creation from the API
            offer_obj = {
                "id": offer_id,
                "offer_title": data.get("offer_title", "Untitled Offer"),
                "status": data.get("status", "active" if data.get("active", False) else "inactive"),
                "active": data.get("active", False),
                "offer_total_budget_allocated": data.get("offer_total_budget_allocated", 0.0),
                "remaining_budget": data.get("remaining_budget", 0.0)
            }
            offers.append(offer_obj)
        
        # Categorize offers by status (same logic as API)
        active_offers = []
        inactive_offers = []
        cancelled_offers = []
        
        for offer in offers:
            status = offer.get("status", "active" if offer.get("active", False) else "inactive")
            if status == "cancelled":
                cancelled_offers.append(offer)
            elif status == "active":
                active_offers.append(offer)
            elif status == "inactive":
                inactive_offers.append(offer)
            else:
                # Fallback for any unexpected status
                if offer.get("active", False):
                    active_offers.append(offer)
                else:
                    inactive_offers.append(offer)
        
        return {
            "status": "success",
            "offers": offers,
            "offers_by_status": {
                "active": active_offers,
                "inactive": inactive_offers,
                "cancelled": cancelled_offers
            },
            "offer_counts": {
                "total": len(offers),
                "active": len(active_offers),
                "inactive": len(inactive_offers),
                "cancelled": len(cancelled_offers)
            }
        }
    
    def test_cancelled_offers_categorization(self) -> bool:
        """Test that cancelled offers appear in the cancelled tab."""
        logger.info("🧪 Testing cancelled offers categorization...")
        
        try:
            # Create test offers with different statuses
            active_offer_id = self.create_test_offer("active", True, "Active")
            inactive_offer_id = self.create_test_offer("inactive", False, "Inactive")
            cancelled_offer_id = self.create_test_offer("cancelled", False, "Cancelled")
            
            # Simulate API call
            api_response = self.simulate_offers_api_call()
            
            # Verify total counts
            if api_response["offer_counts"]["total"] != 3:
                logger.error(f"   ❌ Wrong total count: {api_response['offer_counts']['total']}")
                return False
            
            # Verify active offers
            active_offers = api_response["offers_by_status"]["active"]
            if len(active_offers) != 1 or active_offers[0]["id"] != active_offer_id:
                logger.error(f"   ❌ Wrong active offers: {len(active_offers)}")
                return False
            logger.info(f"   ✅ Active offers: {len(active_offers)} (correct)")
            
            # Verify inactive offers
            inactive_offers = api_response["offers_by_status"]["inactive"]
            if len(inactive_offers) != 1 or inactive_offers[0]["id"] != inactive_offer_id:
                logger.error(f"   ❌ Wrong inactive offers: {len(inactive_offers)}")
                return False
            logger.info(f"   ✅ Inactive offers: {len(inactive_offers)} (correct)")
            
            # Verify cancelled offers
            cancelled_offers = api_response["offers_by_status"]["cancelled"]
            if len(cancelled_offers) != 1 or cancelled_offers[0]["id"] != cancelled_offer_id:
                logger.error(f"   ❌ Wrong cancelled offers: {len(cancelled_offers)}")
                return False
            logger.info(f"   ✅ Cancelled offers: {len(cancelled_offers)} (correct)")
            
            # Verify cancelled offer properties
            cancelled_offer = cancelled_offers[0]
            if cancelled_offer["status"] != "cancelled" or cancelled_offer["active"] != False:
                logger.error(f"   ❌ Wrong cancelled offer properties: status={cancelled_offer['status']}, active={cancelled_offer['active']}")
                return False
            logger.info(f"   ✅ Cancelled offer properties correct: status=cancelled, active=false")
            
            # Verify counts match
            counts = api_response["offer_counts"]
            if (counts["active"] != 1 or counts["inactive"] != 1 or counts["cancelled"] != 1):
                logger.error(f"   ❌ Wrong counts: {counts}")
                return False
            logger.info(f"   ✅ Offer counts correct: active=1, inactive=1, cancelled=1")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Cancelled offers categorization test failed: {e}")
            return False
    
    def test_multiple_cancelled_offers(self) -> bool:
        """Test multiple cancelled offers in the cancelled tab."""
        logger.info("🧪 Testing multiple cancelled offers...")
        
        try:
            # Create multiple cancelled offers
            cancelled_1 = self.create_test_offer("cancelled", False, "Cancelled 1")
            cancelled_2 = self.create_test_offer("cancelled", False, "Cancelled 2")
            cancelled_3 = self.create_test_offer("cancelled", False, "Cancelled 3")
            
            # Simulate API call
            api_response = self.simulate_offers_api_call()
            
            # Verify cancelled offers count
            cancelled_offers = api_response["offers_by_status"]["cancelled"]
            if len(cancelled_offers) != 6:  # 3 from previous test + 3 new ones
                logger.error(f"   ❌ Wrong cancelled offers count: {len(cancelled_offers)}")
                return False
            
            # Verify all cancelled offers have correct properties
            for offer in cancelled_offers:
                if offer["status"] != "cancelled" or offer["active"] != False:
                    logger.error(f"   ❌ Wrong cancelled offer properties: {offer}")
                    return False
            
            logger.info(f"   ✅ All {len(cancelled_offers)} cancelled offers have correct properties")
            
            # Verify counts
            counts = api_response["offer_counts"]
            if counts["cancelled"] != 6:
                logger.error(f"   ❌ Wrong cancelled count: {counts['cancelled']}")
                return False
            
            logger.info(f"   ✅ Cancelled offers count correct: {counts['cancelled']}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Multiple cancelled offers test failed: {e}")
            return False
    
    def run_cancelled_tab_tests(self) -> bool:
        """Run all cancelled tab tests."""
        logger.info("🚀 Starting cancelled offers tab tests...")
        
        try:
            # Setup
            self.cleanup()
            self.setup_test_data()
            
            tests = [
                ("Cancelled Offers Categorization", self.test_cancelled_offers_categorization),
                ("Multiple Cancelled Offers", self.test_multiple_cancelled_offers)
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"📋 Running test: {test_name}")
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
            
            # Final results
            logger.info("=" * 60)
            logger.info("📊 CANCELLED TAB TESTS RESULTS")
            logger.info("=" * 60)
            logger.info(f"Tests passed: {passed}/{total}")
            logger.info("Cancelled offers functionality:")
            logger.info("  - status='cancelled' + active=false → Cancelled tab")
            logger.info("  - Proper categorization by status")
            logger.info("  - Accurate offer counts per category")
            logger.info("=" * 60)
            
            if passed == total:
                logger.info("🎉 All cancelled tab tests PASSED!")
                return True
            else:
                logger.error(f"💥 {total - passed} tests FAILED!")
                return False
                
        except Exception as e:
            logger.error(f"💥 Cancelled tab test suite failed: {e}")
            return False
        finally:
            # Cleanup
            self.cleanup()


def main():
    """Main function to run the cancelled tab tests."""
    tester = CancelledOffersTabTester()
    
    try:
        success = tester.run_cancelled_tab_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
